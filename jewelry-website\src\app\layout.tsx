import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Playfair_Display, Montserrat } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { CartProvider } from "@/contexts/CartContext";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const playfair = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
});

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Luxury Jewelry Co - Premium Jewelry Collection",
  description: "Discover our exquisite collection of luxury jewelry. From elegant rings to stunning necklaces, find the perfect piece for every occasion.",
  keywords: "jewelry, luxury jewelry, rings, necklaces, earrings, bracelets, gold jewelry, diamond jewelry",
  authors: [{ name: "Luxury Jewelry Co" }],
  robots: "index, follow",
  openGraph: {
    title: "Luxury Jewelry Co - Premium Jewelry Collection",
    description: "Discover our exquisite collection of luxury jewelry. From elegant rings to stunning necklaces, find the perfect piece for every occasion.",
    type: "website",
    locale: "en_US",
    siteName: "Luxury Jewelry Co",
  },
  twitter: {
    card: "summary_large_image",
    title: "Luxury Jewelry Co - Premium Jewelry Collection",
    description: "Discover our exquisite collection of luxury jewelry. From elegant rings to stunning necklaces, find the perfect piece for every occasion.",
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#d4af37" />
      </head>
      <body
        className={`${inter.variable} ${playfair.variable} ${montserrat.variable} min-h-screen bg-white text-neutral-900 antialiased`}
      >
        <AuthProvider>
          <CartProvider>
            {children}
          </CartProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
