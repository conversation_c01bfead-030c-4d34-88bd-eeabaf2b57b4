{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/JW/jewelry-website/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Browser client for client components\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Server client for server components\nexport const createServerComponentClient = () => {\n  const cookieStore = cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return cookieStore.get(name)?.value\n      },\n    },\n  })\n}\n\n// Admin client with service role key\nexport const createAdminClient = () => {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!\n  return createClient(supabaseUrl, serviceRoleKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  })\n}\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string\n          email: string\n          full_name: string\n          phone: string\n          address: string\n          user_type: 'customer' | 'business' | 'admin'\n          status: 'active' | 'pending' | 'suspended'\n          business_name?: string\n          gst_number?: string\n          business_address?: string\n          business_type?: string\n          annual_turnover?: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          full_name: string\n          phone: string\n          address: string\n          user_type?: 'customer' | 'business' | 'admin'\n          status?: 'active' | 'pending' | 'suspended'\n          business_name?: string\n          gst_number?: string\n          business_address?: string\n          business_type?: string\n          annual_turnover?: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string\n          phone?: string\n          address?: string\n          user_type?: 'customer' | 'business' | 'admin'\n          status?: 'active' | 'pending' | 'suspended'\n          business_name?: string\n          gst_number?: string\n          business_address?: string\n          business_type?: string\n          annual_turnover?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      categories: {\n        Row: {\n          id: string\n          name: string\n          slug: string\n          description?: string\n          image_url?: string\n          sort_order: number\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          slug: string\n          description?: string\n          image_url?: string\n          sort_order?: number\n          is_active?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          slug?: string\n          description?: string\n          image_url?: string\n          sort_order?: number\n          is_active?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      products: {\n        Row: {\n          id: string\n          name: string\n          slug: string\n          sku: string\n          description: string\n          category_id: string\n          images: string[]\n          weight: number\n          material: string\n          stone_type?: string\n          purity: string\n          b2c_price: number\n          b2b_price: number\n          b2b_discount_percentage: number\n          stock_quantity: number\n          low_stock_threshold: number\n          is_active: boolean\n          specifications: Record<string, any>\n          variations: Record<string, any>[]\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          slug: string\n          sku: string\n          description: string\n          category_id: string\n          images?: string[]\n          weight: number\n          material: string\n          stone_type?: string\n          purity: string\n          b2c_price: number\n          b2b_price: number\n          b2b_discount_percentage?: number\n          stock_quantity?: number\n          low_stock_threshold?: number\n          is_active?: boolean\n          specifications?: Record<string, any>\n          variations?: Record<string, any>[]\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          slug?: string\n          sku?: string\n          description?: string\n          category_id?: string\n          images?: string[]\n          weight?: number\n          material?: string\n          stone_type?: string\n          purity?: string\n          b2c_price?: number\n          b2b_price?: number\n          b2b_discount_percentage?: number\n          stock_quantity?: number\n          low_stock_threshold?: number\n          is_active?: boolean\n          specifications?: Record<string, any>\n          variations?: Record<string, any>[]\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      orders: {\n        Row: {\n          id: string\n          user_id: string\n          order_number: string\n          status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'\n          items: Record<string, any>[]\n          subtotal: number\n          discount: number\n          total: number\n          customer_details: Record<string, any>\n          shipping_address: Record<string, any>\n          notes?: string\n          whatsapp_sent: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          order_number: string\n          status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'\n          items: Record<string, any>[]\n          subtotal: number\n          discount?: number\n          total: number\n          customer_details: Record<string, any>\n          shipping_address: Record<string, any>\n          notes?: string\n          whatsapp_sent?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          order_number?: string\n          status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'\n          items?: Record<string, any>[]\n          subtotal?: number\n          discount?: number\n          total?: number\n          customer_details?: Record<string, any>\n          shipping_address?: Record<string, any>\n          notes?: string\n          whatsapp_sent?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      cart_items: {\n        Row: {\n          id: string\n          user_id: string\n          product_id: string\n          quantity: number\n          variation?: Record<string, any>\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          product_id: string\n          quantity: number\n          variation?: Record<string, any>\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          product_id?: string\n          quantity?: number\n          variation?: Record<string, any>\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      wishlists: {\n        Row: {\n          id: string\n          user_id: string\n          product_id: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          product_id: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          product_id?: string\n          created_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAIoB;AAJpB;AACA;AAAA;AAAA;AACA;;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,8BAA8B;IACzC,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE1B,OAAO,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP,KAAI,IAAY;oBACP;gBAAP,QAAO,mBAAA,YAAY,GAAG,CAAC,mBAAhB,uCAAA,iBAAuB,KAAK;YACrC;QACF;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;IAC5D,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB;QAC/C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/JW/jewelry-website/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { Database } from '@/lib/supabase'\n\ntype UserProfile = Database['public']['Tables']['users']['Row']\n\ninterface AuthContextType {\n  user: User | null\n  profile: UserProfile | null\n  session: Session | null\n  loading: boolean\n  signUp: (email: string, password: string, userData: Partial<UserProfile>) => Promise<{ error: any }>\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signOut: () => Promise<{ error: any }>\n  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n  \n  const supabase = createClientComponentClient()\n\n  useEffect(() => {\n    // Get initial session\n    const getSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setSession(session)\n      setUser(session?.user ?? null)\n      \n      if (session?.user) {\n        await fetchProfile(session.user.id)\n      }\n      \n      setLoading(false)\n    }\n\n    getSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setSession(session)\n        setUser(session?.user ?? null)\n        \n        if (session?.user) {\n          await fetchProfile(session.user.id)\n        } else {\n          setProfile(null)\n        }\n        \n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching profile:', error)\n        return\n      }\n\n      setProfile(data)\n    } catch (error) {\n      console.error('Error fetching profile:', error)\n    }\n  }\n\n  const signUp = async (email: string, password: string, userData: Partial<UserProfile>) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      })\n\n      if (error) return { error }\n\n      if (data.user) {\n        // Create user profile\n        const { error: profileError } = await supabase\n          .from('users')\n          .insert({\n            id: data.user.id,\n            email,\n            ...userData,\n          })\n\n        if (profileError) {\n          console.error('Error creating profile:', profileError)\n          return { error: profileError }\n        }\n      }\n\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      return { error }\n    } catch (error) {\n      return { error }\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut()\n      return { error }\n    } catch (error) {\n      return { error }\n    }\n  }\n\n  const updateProfile = async (updates: Partial<UserProfile>) => {\n    if (!user) return { error: new Error('No user logged in') }\n\n    try {\n      const { error } = await supabase\n        .from('users')\n        .update(updates)\n        .eq('id', user.id)\n\n      if (!error) {\n        setProfile(prev => prev ? { ...prev, ...updates } : null)\n      }\n\n      return { error }\n    } catch (error) {\n      return { error }\n    }\n  }\n\n  const value = {\n    user,\n    profile,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport function useRequireAuth() {\n  const auth = useAuth()\n  \n  useEffect(() => {\n    if (!auth.loading && !auth.user) {\n      window.location.href = '/auth/signin'\n    }\n  }, [auth.loading, auth.user])\n\n  return auth\n}\n\nexport function useRequireAdmin() {\n  const auth = useAuth()\n  \n  useEffect(() => {\n    if (!auth.loading && (!auth.user || auth.profile?.user_type !== 'admin')) {\n      window.location.href = '/'\n    }\n  }, [auth.loading, auth.user, auth.profile])\n\n  return auth\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;;;AAJA;;;AAoBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;qDAAa;oBACjB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAC5D,WAAW;wBACH;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBAEzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;oBACpC;oBAEA,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,WAAW;wBACH;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBAEzB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;wBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;oBACpC,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC;YACF;YAEA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;YACF;YAEA,IAAI,OAAO,OAAO;gBAAE;YAAM;YAE1B,IAAI,KAAK,IAAI,EAAE;gBACb,sBAAsB;gBACtB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB;oBACA,GAAG,QAAQ;gBACb;gBAEF,IAAI,cAAc;oBAChB,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,OAAO;wBAAE,OAAO;oBAAa;gBAC/B;YACF;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7C,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO,IAAI,MAAM;QAAqB;QAE1D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,CAAC,OAAO;gBACV,WAAW,CAAA,OAAQ,OAAO;wBAAE,GAAG,IAAI;wBAAE,GAAG,OAAO;oBAAC,IAAI;YACtD;YAEA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAtJgB;KAAA;AAwJT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAQT,SAAS;;IACd,MAAM,OAAO;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE;gBAC/B,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;mCAAG;QAAC,KAAK,OAAO;QAAE,KAAK,IAAI;KAAC;IAE5B,OAAO;AACT;IAVgB;;QACD;;;AAWR,SAAS;;IACd,MAAM,OAAO;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;gBAC4B;YAApC,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,SAAS,MAAK,OAAO,GAAG;gBACxE,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;oCAAG;QAAC,KAAK,OAAO;QAAE,KAAK,IAAI;QAAE,KAAK,OAAO;KAAC;IAE1C,OAAO;AACT;IAVgB;;QACD", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/JW/jewelry-website/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { Database } from '@/lib/supabase'\nimport { useAuth } from './AuthContext'\n\ntype Product = Database['public']['Tables']['products']['Row']\ntype CartItem = Database['public']['Tables']['cart_items']['Row'] & {\n  product: Product\n}\n\ninterface CartContextType {\n  items: CartItem[]\n  loading: boolean\n  itemCount: number\n  subtotal: number\n  total: number\n  addItem: (productId: string, quantity?: number, variation?: any) => Promise<void>\n  removeItem: (itemId: string) => Promise<void>\n  updateQuantity: (itemId: string, quantity: number) => Promise<void>\n  clearCart: () => Promise<void>\n  refreshCart: () => Promise<void>\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined)\n\nexport function CartProvider({ children }: { children: React.ReactNode }) {\n  const [items, setItems] = useState<CartItem[]>([])\n  const [loading, setLoading] = useState(false)\n  const { user, profile } = useAuth()\n  const supabase = createClientComponentClient()\n\n  // Calculate derived values\n  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0)\n  const subtotal = items.reduce((sum, item) => {\n    const price = profile?.user_type === 'business' ? item.product.b2b_price : item.product.b2c_price\n    return sum + (price * item.quantity)\n  }, 0)\n  const total = subtotal // Add tax/shipping calculations here if needed\n\n  useEffect(() => {\n    if (user) {\n      refreshCart()\n    } else {\n      setItems([])\n    }\n  }, [user])\n\n  const refreshCart = async () => {\n    if (!user) return\n\n    setLoading(true)\n    try {\n      const { data, error } = await supabase\n        .from('cart_items')\n        .select(`\n          *,\n          product:products(*)\n        `)\n        .eq('user_id', user.id)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching cart:', error)\n        return\n      }\n\n      setItems(data as CartItem[])\n    } catch (error) {\n      console.error('Error fetching cart:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addItem = async (productId: string, quantity = 1, variation = {}) => {\n    if (!user) {\n      // Redirect to login or show login modal\n      window.location.href = '/auth/signin'\n      return\n    }\n\n    try {\n      // Check if item already exists with same variation\n      const existingItem = items.find(\n        item => item.product_id === productId && \n        JSON.stringify(item.variation) === JSON.stringify(variation)\n      )\n\n      if (existingItem) {\n        // Update quantity\n        await updateQuantity(existingItem.id, existingItem.quantity + quantity)\n      } else {\n        // Add new item\n        const { error } = await supabase\n          .from('cart_items')\n          .insert({\n            user_id: user.id,\n            product_id: productId,\n            quantity,\n            variation,\n          })\n\n        if (error) {\n          console.error('Error adding to cart:', error)\n          return\n        }\n\n        await refreshCart()\n      }\n    } catch (error) {\n      console.error('Error adding to cart:', error)\n    }\n  }\n\n  const removeItem = async (itemId: string) => {\n    try {\n      const { error } = await supabase\n        .from('cart_items')\n        .delete()\n        .eq('id', itemId)\n\n      if (error) {\n        console.error('Error removing from cart:', error)\n        return\n      }\n\n      setItems(prev => prev.filter(item => item.id !== itemId))\n    } catch (error) {\n      console.error('Error removing from cart:', error)\n    }\n  }\n\n  const updateQuantity = async (itemId: string, quantity: number) => {\n    if (quantity <= 0) {\n      await removeItem(itemId)\n      return\n    }\n\n    try {\n      const { error } = await supabase\n        .from('cart_items')\n        .update({ quantity })\n        .eq('id', itemId)\n\n      if (error) {\n        console.error('Error updating quantity:', error)\n        return\n      }\n\n      setItems(prev => \n        prev.map(item => \n          item.id === itemId ? { ...item, quantity } : item\n        )\n      )\n    } catch (error) {\n      console.error('Error updating quantity:', error)\n    }\n  }\n\n  const clearCart = async () => {\n    if (!user) return\n\n    try {\n      const { error } = await supabase\n        .from('cart_items')\n        .delete()\n        .eq('user_id', user.id)\n\n      if (error) {\n        console.error('Error clearing cart:', error)\n        return\n      }\n\n      setItems([])\n    } catch (error) {\n      console.error('Error clearing cart:', error)\n    }\n  }\n\n  const value = {\n    items,\n    loading,\n    itemCount,\n    subtotal,\n    total,\n    addItem,\n    removeItem,\n    updateQuantity,\n    clearCart,\n    refreshCart,\n  }\n\n  return (\n    <CartContext.Provider value={value}>\n      {children}\n    </CartContext.Provider>\n  )\n}\n\nexport function useCart() {\n  const context = useContext(CartContext)\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;;;AALA;;;;AAyBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAE3C,2BAA2B;IAC3B,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;IACnE,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK;QAClC,MAAM,QAAQ,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAK,aAAa,KAAK,OAAO,CAAC,SAAS,GAAG,KAAK,OAAO,CAAC,SAAS;QACjG,OAAO,MAAO,QAAQ,KAAK,QAAQ;IACrC,GAAG;IACH,MAAM,QAAQ,SAAS,+CAA+C;;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM;gBACR;YACF,OAAO;gBACL,SAAS,EAAE;YACb;QACF;iCAAG;QAAC;KAAK;IAET,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAE,2DAIR,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wBAAwB;gBACtC;YACF;YAEA,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU,eAAO;YAAmB,4EAAW,GAAG,6EAAY,CAAC;QACnE,IAAI,CAAC,MAAM;YACT,wCAAwC;YACxC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACvB;QACF;QAEA,IAAI;YACF,mDAAmD;YACnD,MAAM,eAAe,MAAM,IAAI,CAC7B,CAAA,OAAQ,KAAK,UAAU,KAAK,aAC5B,KAAK,SAAS,CAAC,KAAK,SAAS,MAAM,KAAK,SAAS,CAAC;YAGpD,IAAI,cAAc;gBAChB,kBAAkB;gBAClB,MAAM,eAAe,aAAa,EAAE,EAAE,aAAa,QAAQ,GAAG;YAChE,OAAO;gBACL,eAAe;gBACf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,cACL,MAAM,CAAC;oBACN,SAAS,KAAK,EAAE;oBAChB,YAAY;oBACZ;oBACA;gBACF;gBAEF,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,yBAAyB;oBACvC;gBACF;gBAEA,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C;YACF;YAEA,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,iBAAiB,OAAO,QAAgB;QAC5C,IAAI,YAAY,GAAG;YACjB,MAAM,WAAW;YACjB;QACF;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,cACL,MAAM,CAAC;gBAAE;YAAS,GAClB,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C;YACF;YAEA,SAAS,CAAA,OACP,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SAAS;wBAAE,GAAG,IAAI;wBAAE;oBAAS,IAAI;QAGnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,WAAW,KAAK,EAAE;YAExB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wBAAwB;gBACtC;YACF;YAEA,SAAS,EAAE;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA5KgB;;QAGY,kIAAA,CAAA,UAAO;;;KAHnB;AA8KT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}