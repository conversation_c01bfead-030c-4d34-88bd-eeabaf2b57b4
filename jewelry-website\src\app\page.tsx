import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import Link from 'next/link'
import {
  Crown,
  Star,
  Shield,
  Truck,
  Award,
  ArrowRight,
  Sparkles,
  Heart,
  Gift
} from 'lucide-react'

const categories = [
  {
    name: 'Rings',
    href: '/products/rings',
    image: '/placeholder-ring.jpg',
    description: 'Elegant rings for every occasion'
  },
  {
    name: 'Necklaces',
    href: '/products/necklaces',
    image: '/placeholder-necklace.jpg',
    description: 'Stunning necklaces and chains'
  },
  {
    name: 'Earrings',
    href: '/products/earrings',
    image: '/placeholder-earrings.jpg',
    description: 'Beautiful earrings collection'
  },
  {
    name: 'Bracelets',
    href: '/products/bracelets',
    image: '/placeholder-bracelet.jpg',
    description: 'Stylish bracelets and bangles'
  }
]

const features = [
  {
    icon: Crown,
    title: 'Premium Quality',
    description: 'Handcrafted with the finest materials and attention to detail'
  },
  {
    icon: Shield,
    title: 'Lifetime Warranty',
    description: 'Comprehensive warranty on all our jewelry pieces'
  },
  {
    icon: Truck,
    title: 'Free Shipping',
    description: 'Complimentary shipping on orders above ₹10,000'
  },
  {
    icon: Award,
    title: 'Certified Authentic',
    description: 'All jewelry comes with authenticity certificates'
  }
]

const testimonials = [
  {
    name: 'Priya Sharma',
    role: 'Happy Customer',
    content: 'Absolutely stunning jewelry! The quality is exceptional and the customer service is outstanding.',
    rating: 5
  },
  {
    name: 'Rajesh Kumar',
    role: 'Business Client',
    content: 'As a jewelry retailer, I appreciate their B2B pricing and reliable service. Highly recommended!',
    rating: 5
  },
  {
    name: 'Anita Patel',
    role: 'Bride',
    content: 'Found the perfect wedding jewelry here. The designs are elegant and timeless.',
    rating: 5
  }
]

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 via-white to-secondary-50 hero-pattern">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="animate-fade-in">
              <h1 className="text-5xl lg:text-7xl font-bold font-serif text-neutral-900 mb-6">
                Luxury
                <span className="block text-primary-500">Jewelry</span>
                <span className="block">Collection</span>
              </h1>
              <p className="text-xl text-neutral-600 mb-8 max-w-lg">
                Discover our exquisite collection of handcrafted jewelry. From elegant rings to stunning necklaces,
                find the perfect piece for every special moment.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/products">
                  <Button variant="luxury" size="lg" className="w-full sm:w-auto">
                    <Sparkles className="mr-2 h-5 w-5" />
                    Explore Collection
                  </Button>
                </Link>
                <Link href="/about">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Our Story
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>
            </div>

            <div className="relative animate-float">
              <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-luxury-lg">
                <div className="absolute inset-0 luxury-gradient opacity-20"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <Crown className="h-32 w-32 text-primary-500 animate-pulse-glow" />
                </div>
              </div>
              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-primary-500 rounded-full flex items-center justify-center shadow-glow animate-float">
                <Star className="h-8 w-8 text-white" />
              </div>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-secondary-500 rounded-full flex items-center justify-center shadow-glow animate-float" style={{animationDelay: '1s'}}>
                <Heart className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-neutral-900 mb-4">
              Shop by Category
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Explore our carefully curated collections designed for every style and occasion
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {categories.map((category, index) => (
              <Link key={category.name} href={category.href}>
                <Card className="group hover:shadow-luxury-lg transition-all duration-300 transform hover:-translate-y-2">
                  <div className="relative h-64 bg-gradient-to-br from-primary-50 to-secondary-50 rounded-t-xl overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Gift className="h-16 w-16 text-primary-500 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-neutral-900 mb-2 group-hover:text-primary-500 transition-colors duration-300">
                      {category.name}
                    </h3>
                    <p className="text-neutral-600">
                      {category.description}
                    </p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-neutral-900 mb-4">
              Why Choose Us
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              We're committed to providing you with the finest jewelry and exceptional service
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={feature.title} variant="luxury" className="text-center p-8 group hover:shadow-luxury-lg transition-all duration-300">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-6 group-hover:bg-primary-500 transition-colors duration-300">
                  <feature.icon className="h-8 w-8 text-primary-500 group-hover:text-white transition-colors duration-300" />
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-4">
                  {feature.title}
                </h3>
                <p className="text-neutral-600">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-neutral-900 mb-4">
              What Our Customers Say
            </h2>
            <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
              Don't just take our word for it - hear from our satisfied customers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={testimonial.name} variant="elevated" className="p-8">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-primary-500 fill-current" />
                  ))}
                </div>
                <p className="text-neutral-600 mb-6 italic">
                  "{testimonial.content}"
                </p>
                <div>
                  <p className="font-semibold text-neutral-900">{testimonial.name}</p>
                  <p className="text-sm text-neutral-500">{testimonial.role}</p>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 luxury-gradient">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold font-serif text-white mb-6">
            Ready to Find Your Perfect Piece?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Browse our complete collection or get in touch with our experts for personalized recommendations
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/products">
              <Button variant="secondary" size="lg" className="w-full sm:w-auto">
                <Sparkles className="mr-2 h-5 w-5" />
                Browse Collection
              </Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline" size="lg" className="w-full sm:w-auto bg-white/10 border-white text-white hover:bg-white hover:text-primary-500">
                Contact Expert
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
