'use client'

import { <PERSON><PERSON> } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { useCart } from '@/contexts/CartContext'
import { useAuth } from '@/contexts/AuthContext'
import { Crown, Minus, Plus, Trash2, ShoppingBag, ArrowRight, Gift } from 'lucide-react'
import Link from 'next/link'
import { formatPrice } from '@/lib/utils'

export default function CartPage() {
  const { items, loading, itemCount, subtotal, total, updateQuantity, removeItem } = useCart()
  const { user, profile } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="mt-4 text-neutral-600">Loading your cart...</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <Card variant="luxury" className="text-center p-12">
            <ShoppingBag className="h-16 w-16 text-neutral-400 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold text-neutral-900 mb-4">
              Sign in to view your cart
            </h2>
            <p className="text-neutral-600 mb-8">
              Please sign in to your account to access your shopping cart and continue with your purchase.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/signin">
                <Button variant="luxury" size="lg">
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/signup">
                <Button variant="outline" size="lg">
                  Create Account
                </Button>
              </Link>
            </div>
          </Card>
        </div>
        <Footer />
      </div>
    )
  }

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <Card variant="luxury" className="text-center p-12">
            <ShoppingBag className="h-16 w-16 text-neutral-400 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold text-neutral-900 mb-4">
              Your cart is empty
            </h2>
            <p className="text-neutral-600 mb-8">
              Looks like you haven't added any items to your cart yet. Start shopping to fill it up!
            </p>
            <Link href="/products">
              <Button variant="luxury" size="lg">
                <Gift className="mr-2 h-5 w-5" />
                Start Shopping
              </Button>
            </Link>
          </Card>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Page Header */}
      <section className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold font-serif mb-2">
              Shopping Cart
            </h1>
            <p className="text-white/90">
              {itemCount} {itemCount === 1 ? 'item' : 'items'} in your cart
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ShoppingBag className="h-5 w-5" />
                  <span>Cart Items</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {items.map((item) => {
                  const price = profile?.user_type === 'business' ? item.product.b2b_price : item.product.b2c_price
                  
                  return (
                    <div key={item.id} className="flex items-center space-x-4 p-4 border border-neutral-200 rounded-lg">
                      {/* Product Image */}
                      <div className="w-20 h-20 bg-gradient-to-br from-primary-50 to-secondary-50 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Crown className="h-8 w-8 text-primary-500" />
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-neutral-900 truncate">
                          {item.product.name}
                        </h3>
                        <p className="text-sm text-neutral-600">
                          SKU: {item.product.sku}
                        </p>
                        <p className="text-sm text-neutral-600">
                          Weight: {item.product.weight}g
                        </p>
                        {item.variation && Object.keys(item.variation).length > 0 && (
                          <div className="text-sm text-neutral-600">
                            {Object.entries(item.variation).map(([key, value]) => (
                              <span key={key} className="mr-2">
                                {key}: {value}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Price */}
                      <div className="text-right">
                        <p className="font-semibold text-neutral-900">
                          {formatPrice(price)}
                        </p>
                        {profile?.user_type === 'business' && (
                          <p className="text-xs text-green-600">
                            B2B Price
                          </p>
                        )}
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-1 rounded-full hover:bg-neutral-100 transition-colors"
                          disabled={item.quantity <= 1}
                        >
                          <Minus className="h-4 w-4" />
                        </button>
                        <span className="w-8 text-center font-medium">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-1 rounded-full hover:bg-neutral-100 transition-colors"
                        >
                          <Plus className="h-4 w-4" />
                        </button>
                      </div>

                      {/* Total Price */}
                      <div className="text-right">
                        <p className="font-semibold text-neutral-900">
                          {formatPrice(price * item.quantity)}
                        </p>
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeItem(item.id)}
                        className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  )
                })}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <Card variant="luxury">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-neutral-600">Subtotal</span>
                  <span className="font-medium">{formatPrice(subtotal)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-neutral-600">Shipping</span>
                  <span className="font-medium text-green-600">Free</span>
                </div>

                {profile?.user_type === 'business' && (
                  <div className="flex justify-between text-green-600">
                    <span>B2B Discount</span>
                    <span className="font-medium">Applied</span>
                  </div>
                )}

                <hr className="border-neutral-200" />

                <div className="flex justify-between text-lg font-semibold">
                  <span>Total</span>
                  <span>{formatPrice(total)}</span>
                </div>

                <Button variant="luxury" size="lg" className="w-full">
                  Proceed to Checkout
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>

                <p className="text-xs text-neutral-500 text-center">
                  Orders will be processed via WhatsApp for secure communication
                </p>
              </CardContent>
            </Card>

            {/* Coupon Code */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Coupon Code</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex space-x-2">
                  <Input
                    placeholder="Enter coupon code"
                    className="flex-1"
                  />
                  <Button variant="outline">
                    Apply
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Continue Shopping */}
            <Link href="/products">
              <Button variant="outline" size="lg" className="w-full">
                Continue Shopping
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
