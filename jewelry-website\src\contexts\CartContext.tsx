'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClientComponentClient } from '@/lib/supabase'
import { Database } from '@/lib/supabase'
import { useAuth } from './AuthContext'

type Product = Database['public']['Tables']['products']['Row']
type CartItem = Database['public']['Tables']['cart_items']['Row'] & {
  product: Product
}

interface CartContextType {
  items: CartItem[]
  loading: boolean
  itemCount: number
  subtotal: number
  total: number
  addItem: (productId: string, quantity?: number, variation?: any) => Promise<void>
  removeItem: (itemId: string) => Promise<void>
  updateQuantity: (itemId: string, quantity: number) => Promise<void>
  clearCart: () => Promise<void>
  refreshCart: () => Promise<void>
}

const CartContext = createContext<CartContextType | undefined>(undefined)

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([])
  const [loading, setLoading] = useState(false)
  const { user, profile } = useAuth()
  const supabase = createClientComponentClient()

  // Calculate derived values
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0)
  const subtotal = items.reduce((sum, item) => {
    const price = profile?.user_type === 'business' ? item.product.b2b_price : item.product.b2c_price
    return sum + (price * item.quantity)
  }, 0)
  const total = subtotal // Add tax/shipping calculations here if needed

  useEffect(() => {
    if (user) {
      refreshCart()
    } else {
      setItems([])
    }
  }, [user])

  const refreshCart = async () => {
    if (!user) return

    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('cart_items')
        .select(`
          *,
          product:products(*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching cart:', error)
        return
      }

      setItems(data as CartItem[])
    } catch (error) {
      console.error('Error fetching cart:', error)
    } finally {
      setLoading(false)
    }
  }

  const addItem = async (productId: string, quantity = 1, variation = {}) => {
    if (!user) {
      // Redirect to login or show login modal
      window.location.href = '/auth/signin'
      return
    }

    try {
      // Check if item already exists with same variation
      const existingItem = items.find(
        item => item.product_id === productId && 
        JSON.stringify(item.variation) === JSON.stringify(variation)
      )

      if (existingItem) {
        // Update quantity
        await updateQuantity(existingItem.id, existingItem.quantity + quantity)
      } else {
        // Add new item
        const { error } = await supabase
          .from('cart_items')
          .insert({
            user_id: user.id,
            product_id: productId,
            quantity,
            variation,
          })

        if (error) {
          console.error('Error adding to cart:', error)
          return
        }

        await refreshCart()
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
    }
  }

  const removeItem = async (itemId: string) => {
    try {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('id', itemId)

      if (error) {
        console.error('Error removing from cart:', error)
        return
      }

      setItems(prev => prev.filter(item => item.id !== itemId))
    } catch (error) {
      console.error('Error removing from cart:', error)
    }
  }

  const updateQuantity = async (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      await removeItem(itemId)
      return
    }

    try {
      const { error } = await supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', itemId)

      if (error) {
        console.error('Error updating quantity:', error)
        return
      }

      setItems(prev => 
        prev.map(item => 
          item.id === itemId ? { ...item, quantity } : item
        )
      )
    } catch (error) {
      console.error('Error updating quantity:', error)
    }
  }

  const clearCart = async () => {
    if (!user) return

    try {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('user_id', user.id)

      if (error) {
        console.error('Error clearing cart:', error)
        return
      }

      setItems([])
    } catch (error) {
      console.error('Error clearing cart:', error)
    }
  }

  const value = {
    items,
    loading,
    itemCount,
    subtotal,
    total,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    refreshCart,
  }

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}

export function useCart() {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}
