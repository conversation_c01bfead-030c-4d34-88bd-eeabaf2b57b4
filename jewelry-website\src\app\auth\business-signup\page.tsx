'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Crown, Eye, EyeOff, Mail, Lock, User, MapPin, Phone, Building, CreditCard, DollarSign } from 'lucide-react'
import { validateEmail, validatePhone, validateGST } from '@/lib/utils'

const businessTypes = [
  'Jewelry Retailer',
  'Jewelry Manufacturer', 
  'Jewelry Wholesaler',
  'Online Jewelry Store',
  'Jewelry Designer',
  'Other'
]

const annualTurnoverRanges = [
  'Under ₹10 Lakhs',
  '₹10 Lakhs - ₹50 Lakhs',
  '₹50 Lakhs - ₹1 Crore',
  '₹1 Crore - ₹5 Crores',
  '₹5 Crores - ₹10 Crores',
  'Above ₹10 Crores'
]

export default function BusinessSignUpPage() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    address: '',
    password: '',
    confirmPassword: '',
    businessName: '',
    gstNumber: '',
    businessAddress: '',
    businessType: '',
    annualTurnover: '',
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  const { signUp } = useAuth()
  const router = useRouter()

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Personal Information
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Contact person name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Personal address is required'
    }

    // Business Information
    if (!formData.businessName.trim()) {
      newErrors.businessName = 'Business name is required'
    }

    if (!formData.gstNumber.trim()) {
      newErrors.gstNumber = 'GST number is required'
    } else if (!validateGST(formData.gstNumber)) {
      newErrors.gstNumber = 'Please enter a valid GST number'
    }

    if (!formData.businessAddress.trim()) {
      newErrors.businessAddress = 'Business address is required'
    }

    if (!formData.businessType) {
      newErrors.businessType = 'Please select your business type'
    }

    if (!formData.annualTurnover) {
      newErrors.annualTurnover = 'Please select your annual turnover range'
    }

    // Password
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)

    const { error } = await signUp(formData.email, formData.password, {
      full_name: formData.fullName,
      phone: formData.phone,
      address: formData.address,
      user_type: 'business',
      status: 'pending', // Business accounts need admin approval
      business_name: formData.businessName,
      gst_number: formData.gstNumber,
      business_address: formData.businessAddress,
      business_type: formData.businessType,
      annual_turnover: formData.annualTurnover,
    })
    
    if (error) {
      setErrors({ submit: error.message })
    } else {
      router.push('/auth/business-pending')
    }
    
    setLoading(false)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 hero-pattern flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8">
        {/* Logo */}
        <div className="text-center">
          <Link href="/" className="inline-flex items-center space-x-2">
            <Crown className="h-12 w-12 text-primary-500" />
            <span className="text-3xl font-bold font-serif text-primary-500">
              Luxury Jewelry Co
            </span>
          </Link>
          <h2 className="mt-6 text-3xl font-bold text-neutral-900">
            Business Account Registration
          </h2>
          <p className="mt-2 text-sm text-neutral-600">
            Join our B2B program and enjoy wholesale pricing and exclusive benefits
          </p>
        </div>

        {/* Sign Up Form */}
        <Card variant="luxury" className="shadow-luxury-lg">
          <CardHeader>
            <CardTitle className="text-center text-2xl font-semibold text-neutral-900">
              Business Registration
            </CardTitle>
            <p className="text-center text-sm text-neutral-600 mt-2">
              Your account will be reviewed and approved within 24-48 hours
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {errors.submit && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-sm text-red-600">{errors.submit}</p>
                </div>
              )}

              {/* Personal Information Section */}
              <div>
                <h3 className="text-lg font-semibold text-neutral-900 mb-4 border-b border-neutral-200 pb-2">
                  Contact Person Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="relative">
                    <Input
                      label="Contact Person Name"
                      type="text"
                      value={formData.fullName}
                      onChange={(e) => handleInputChange('fullName', e.target.value)}
                      placeholder="Enter contact person name"
                      error={errors.fullName}
                      className="pl-10"
                    />
                    <User className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                  </div>

                  <div className="relative">
                    <Input
                      label="Email Address"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="Enter email address"
                      error={errors.email}
                      className="pl-10"
                    />
                    <Mail className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                  </div>

                  <div className="relative">
                    <Input
                      label="Phone Number"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="Enter phone number"
                      error={errors.phone}
                      className="pl-10"
                    />
                    <Phone className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                  </div>

                  <div className="relative">
                    <Input
                      label="Personal Address"
                      type="text"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="Enter personal address"
                      error={errors.address}
                      className="pl-10"
                    />
                    <MapPin className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                  </div>
                </div>
              </div>

              {/* Business Information Section */}
              <div>
                <h3 className="text-lg font-semibold text-neutral-900 mb-4 border-b border-neutral-200 pb-2">
                  Business Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="relative">
                    <Input
                      label="Business Name"
                      type="text"
                      value={formData.businessName}
                      onChange={(e) => handleInputChange('businessName', e.target.value)}
                      placeholder="Enter business name"
                      error={errors.businessName}
                      className="pl-10"
                    />
                    <Building className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                  </div>

                  <div className="relative">
                    <Input
                      label="GST Number"
                      type="text"
                      value={formData.gstNumber}
                      onChange={(e) => handleInputChange('gstNumber', e.target.value.toUpperCase())}
                      placeholder="Enter GST number"
                      error={errors.gstNumber}
                      className="pl-10"
                    />
                    <CreditCard className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                  </div>

                  <div className="md:col-span-2 relative">
                    <Input
                      label="Business Address"
                      type="text"
                      value={formData.businessAddress}
                      onChange={(e) => handleInputChange('businessAddress', e.target.value)}
                      placeholder="Enter complete business address"
                      error={errors.businessAddress}
                      className="pl-10"
                    />
                    <MapPin className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Business Type
                    </label>
                    <select
                      value={formData.businessType}
                      onChange={(e) => handleInputChange('businessType', e.target.value)}
                      className="w-full h-12 px-4 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20"
                    >
                      <option value="">Select business type</option>
                      {businessTypes.map((type) => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))}
                    </select>
                    {errors.businessType && (
                      <p className="mt-1 text-sm text-red-600">{errors.businessType}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Annual Turnover
                    </label>
                    <select
                      value={formData.annualTurnover}
                      onChange={(e) => handleInputChange('annualTurnover', e.target.value)}
                      className="w-full h-12 px-4 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20"
                    >
                      <option value="">Select turnover range</option>
                      {annualTurnoverRanges.map((range) => (
                        <option key={range} value={range}>
                          {range}
                        </option>
                      ))}
                    </select>
                    {errors.annualTurnover && (
                      <p className="mt-1 text-sm text-red-600">{errors.annualTurnover}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Password Section */}
              <div>
                <h3 className="text-lg font-semibold text-neutral-900 mb-4 border-b border-neutral-200 pb-2">
                  Account Security
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="relative">
                    <Input
                      label="Password"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="Create a password"
                      error={errors.password}
                      className="pl-10 pr-10"
                    />
                    <Lock className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-9 h-5 w-5 text-neutral-400 hover:text-neutral-600"
                    >
                      {showPassword ? <EyeOff /> : <Eye />}
                    </button>
                  </div>

                  <div className="relative">
                    <Input
                      label="Confirm Password"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      placeholder="Confirm your password"
                      error={errors.confirmPassword}
                      className="pl-10 pr-10"
                    />
                    <Lock className="absolute left-3 top-9 h-5 w-5 text-neutral-400" />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-9 h-5 w-5 text-neutral-400 hover:text-neutral-600"
                    >
                      {showConfirmPassword ? <EyeOff /> : <Eye />}
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                <input
                  id="agree-terms"
                  name="agree-terms"
                  type="checkbox"
                  required
                  className="h-4 w-4 text-primary-500 focus:ring-primary-500 border-neutral-300 rounded"
                />
                <label htmlFor="agree-terms" className="ml-2 block text-sm text-neutral-700">
                  I agree to the{' '}
                  <Link href="/terms" className="text-primary-500 hover:text-primary-600">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-primary-500 hover:text-primary-600">
                    Privacy Policy
                  </Link>
                </label>
              </div>

              <Button
                type="submit"
                variant="luxury"
                size="lg"
                className="w-full"
                loading={loading}
              >
                Submit Business Registration
              </Button>
            </form>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-neutral-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-neutral-500">Already have an account?</span>
                </div>
              </div>

              <div className="mt-6">
                <Link href="/auth/signin">
                  <Button variant="outline" size="lg" className="w-full">
                    Sign In
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Account Link */}
        <div className="text-center">
          <p className="text-sm text-neutral-600">
            Looking for a personal account?{' '}
            <Link
              href="/auth/signup"
              className="font-medium text-primary-500 hover:text-primary-600"
            >
              Sign up as a customer
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
