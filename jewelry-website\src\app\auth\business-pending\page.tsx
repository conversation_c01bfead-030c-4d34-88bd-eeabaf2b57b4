import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Crown, Clock, CheckCircle, Mail, Phone } from 'lucide-react'

export default function BusinessPendingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 hero-pattern flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo */}
        <div className="text-center">
          <Link href="/" className="inline-flex items-center space-x-2">
            <Crown className="h-12 w-12 text-primary-500" />
            <span className="text-3xl font-bold font-serif text-primary-500">
              Luxury Jewelry Co
            </span>
          </Link>
        </div>

        {/* Pending Approval Card */}
        <Card variant="luxury" className="shadow-luxury-lg">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
            <CardTitle className="text-2xl font-semibold text-neutral-900">
              Registration Submitted
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-6">
            <div>
              <p className="text-lg text-neutral-700 mb-4">
                Thank you for your business registration!
              </p>
              <p className="text-neutral-600">
                Your application has been submitted successfully and is currently under review. 
                Our team will verify your business details and approve your account within 24-48 hours.
              </p>
            </div>

            <div className="bg-primary-50 rounded-lg p-4">
              <h3 className="font-semibold text-neutral-900 mb-2">What happens next?</h3>
              <div className="space-y-2 text-sm text-neutral-600">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Application received and logged</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-yellow-500" />
                  <span>Business verification in progress</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-neutral-400" />
                  <span>Email notification upon approval</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-neutral-400" />
                  <span>Access to B2B pricing and features</span>
                </div>
              </div>
            </div>

            <div className="border-t border-neutral-200 pt-6">
              <h3 className="font-semibold text-neutral-900 mb-2">Need assistance?</h3>
              <p className="text-sm text-neutral-600 mb-4">
                If you have any questions about your application or need immediate assistance, 
                please don't hesitate to contact us.
              </p>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-center space-x-2 text-neutral-600">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-neutral-600">
                  <Phone className="h-4 w-4" />
                  <span>******-567-8900</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Link href="/">
                <Button variant="luxury" size="lg" className="w-full">
                  Continue Shopping
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button variant="outline" size="lg" className="w-full">
                  Sign In
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <div className="text-center text-sm text-neutral-600">
          <p>
            You will receive an email confirmation once your business account is approved. 
            In the meantime, you can browse our catalog as a guest.
          </p>
        </div>
      </div>
    </div>
  )
}
