import { createClient } from '@supabase/supabase-js'
import { createBrowserClient, createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Browser client for client components
export const createClientComponentClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Server client for server components
export const createServerComponentClient = () => {
  const cookieStore = cookies()
  
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value
      },
    },
  })
}

// Admin client with service role key
export const createAdminClient = () => {
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          phone: string
          address: string
          user_type: 'customer' | 'business' | 'admin'
          status: 'active' | 'pending' | 'suspended'
          business_name?: string
          gst_number?: string
          business_address?: string
          business_type?: string
          annual_turnover?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name: string
          phone: string
          address: string
          user_type?: 'customer' | 'business' | 'admin'
          status?: 'active' | 'pending' | 'suspended'
          business_name?: string
          gst_number?: string
          business_address?: string
          business_type?: string
          annual_turnover?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          phone?: string
          address?: string
          user_type?: 'customer' | 'business' | 'admin'
          status?: 'active' | 'pending' | 'suspended'
          business_name?: string
          gst_number?: string
          business_address?: string
          business_type?: string
          annual_turnover?: string
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description?: string
          image_url?: string
          sort_order: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string
          image_url?: string
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string
          image_url?: string
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          slug: string
          sku: string
          description: string
          category_id: string
          images: string[]
          weight: number
          material: string
          stone_type?: string
          purity: string
          b2c_price: number
          b2b_price: number
          b2b_discount_percentage: number
          stock_quantity: number
          low_stock_threshold: number
          is_active: boolean
          specifications: Record<string, any>
          variations: Record<string, any>[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          sku: string
          description: string
          category_id: string
          images?: string[]
          weight: number
          material: string
          stone_type?: string
          purity: string
          b2c_price: number
          b2b_price: number
          b2b_discount_percentage?: number
          stock_quantity?: number
          low_stock_threshold?: number
          is_active?: boolean
          specifications?: Record<string, any>
          variations?: Record<string, any>[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          sku?: string
          description?: string
          category_id?: string
          images?: string[]
          weight?: number
          material?: string
          stone_type?: string
          purity?: string
          b2c_price?: number
          b2b_price?: number
          b2b_discount_percentage?: number
          stock_quantity?: number
          low_stock_threshold?: number
          is_active?: boolean
          specifications?: Record<string, any>
          variations?: Record<string, any>[]
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          user_id: string
          order_number: string
          status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          items: Record<string, any>[]
          subtotal: number
          discount: number
          total: number
          customer_details: Record<string, any>
          shipping_address: Record<string, any>
          notes?: string
          whatsapp_sent: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          order_number: string
          status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          items: Record<string, any>[]
          subtotal: number
          discount?: number
          total: number
          customer_details: Record<string, any>
          shipping_address: Record<string, any>
          notes?: string
          whatsapp_sent?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          order_number?: string
          status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          items?: Record<string, any>[]
          subtotal?: number
          discount?: number
          total?: number
          customer_details?: Record<string, any>
          shipping_address?: Record<string, any>
          notes?: string
          whatsapp_sent?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      cart_items: {
        Row: {
          id: string
          user_id: string
          product_id: string
          quantity: number
          variation?: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          quantity: number
          variation?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          quantity?: number
          variation?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
      }
      wishlists: {
        Row: {
          id: string
          user_id: string
          product_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          created_at?: string
        }
      }
    }
  }
}
