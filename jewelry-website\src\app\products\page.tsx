import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Crown, Filter, Search, Grid, List, Heart, ShoppingBag } from 'lucide-react'

// Mock product data
const products = [
  {
    id: 1,
    name: 'Diamond Solitaire Ring',
    category: 'Rings',
    price: 125000,
    b2bPrice: 100000,
    image: '/placeholder-ring.jpg',
    rating: 4.8,
    reviews: 24,
    inStock: true,
  },
  {
    id: 2,
    name: 'Gold Chain Necklace',
    category: 'Necklaces',
    price: 85000,
    b2bPrice: 68000,
    image: '/placeholder-necklace.jpg',
    rating: 4.9,
    reviews: 18,
    inStock: true,
  },
  {
    id: 3,
    name: 'Pearl Drop Earrings',
    category: 'Earrings',
    price: 45000,
    b2bPrice: 36000,
    image: '/placeholder-earrings.jpg',
    rating: 4.7,
    reviews: 32,
    inStock: false,
  },
  {
    id: 4,
    name: 'Tennis Bracelet',
    category: 'Bracelets',
    price: 95000,
    b2bPrice: 76000,
    image: '/placeholder-bracelet.jpg',
    rating: 4.8,
    reviews: 15,
    inStock: true,
  },
  {
    id: 5,
    name: 'Emerald Pendant',
    category: 'Pendants',
    price: 75000,
    b2bPrice: 60000,
    image: '/placeholder-pendant.jpg',
    rating: 4.6,
    reviews: 21,
    inStock: true,
  },
  {
    id: 6,
    name: 'Rose Gold Band',
    category: 'Rings',
    price: 55000,
    b2bPrice: 44000,
    image: '/placeholder-ring-2.jpg',
    rating: 4.9,
    reviews: 28,
    inStock: true,
  },
]

const categories = [
  'All Products',
  'Rings',
  'Necklaces',
  'Earrings',
  'Bracelets',
  'Pendants',
  'Chains',
  'Anklets',
  'Nose Pins',
  'Bangles',
]

const priceRanges = [
  'All Prices',
  'Under ₹50,000',
  '₹50,000 - ₹1,00,000',
  '₹1,00,000 - ₹2,00,000',
  'Above ₹2,00,000',
]

export default function ProductsPage() {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price).replace('₹', '₹')
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold font-serif mb-4">
              Our Jewelry Collection
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Discover our exquisite range of handcrafted jewelry pieces, each designed to perfection
            </p>
          </div>
        </div>
      </section>

      {/* Filters and Search */}
      <section className="py-8 border-b border-neutral-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400" />
              <input
                type="text"
                placeholder="Search jewelry..."
                className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-4">
              <select className="px-4 py-3 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none">
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>

              <select className="px-4 py-3 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none">
                {priceRanges.map((range) => (
                  <option key={range} value={range}>
                    {range}
                  </option>
                ))}
              </select>

              <Button variant="outline" className="px-4 py-3">
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>

            {/* View Toggle */}
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm">
                <Grid className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-semibold text-neutral-900">
              All Products ({products.length})
            </h2>
            <select className="px-4 py-2 border border-neutral-300 rounded-lg focus:border-primary-500 focus:outline-none">
              <option>Sort by: Featured</option>
              <option>Price: Low to High</option>
              <option>Price: High to Low</option>
              <option>Newest First</option>
              <option>Customer Rating</option>
            </select>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {products.map((product) => (
              <Card key={product.id} className="group hover:shadow-luxury-lg transition-all duration-300 transform hover:-translate-y-1">
                <div className="relative">
                  {/* Product Image */}
                  <div className="aspect-square bg-gradient-to-br from-primary-50 to-secondary-50 rounded-t-xl overflow-hidden">
                    <div className="w-full h-full flex items-center justify-center">
                      <Crown className="h-16 w-16 text-primary-500 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                  </div>

                  {/* Wishlist Button */}
                  <button className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow duration-200">
                    <Heart className="h-4 w-4 text-neutral-600 hover:text-red-500" />
                  </button>

                  {/* Stock Status */}
                  {!product.inStock && (
                    <div className="absolute inset-0 bg-black/50 rounded-t-xl flex items-center justify-center">
                      <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Out of Stock
                      </span>
                    </div>
                  )}
                </div>

                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-primary-500 font-medium uppercase tracking-wide">
                        {product.category}
                      </span>
                      <div className="flex items-center space-x-1">
                        <Crown className="h-3 w-3 text-yellow-400" />
                        <span className="text-xs text-neutral-600">
                          {product.rating} ({product.reviews})
                        </span>
                      </div>
                    </div>

                    <h3 className="font-semibold text-neutral-900 group-hover:text-primary-500 transition-colors duration-200">
                      {product.name}
                    </h3>

                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-neutral-900">
                          {formatPrice(product.price)}
                        </span>
                        <span className="text-sm text-green-600 font-medium">
                          B2B: {formatPrice(product.b2bPrice)}
                        </span>
                      </div>
                    </div>

                    <div className="flex space-x-2 pt-2">
                      <Button
                        variant="luxury"
                        size="sm"
                        className="flex-1"
                        disabled={!product.inStock}
                      >
                        <ShoppingBag className="h-4 w-4 mr-1" />
                        Add to Cart
                      </Button>
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Products
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
