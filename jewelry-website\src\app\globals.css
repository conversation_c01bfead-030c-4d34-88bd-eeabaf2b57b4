@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  /* Luxury Color Palette */
  --color-primary-50: #fdf8f0;
  --color-primary-100: #faebd7;
  --color-primary-200: #f4d4a7;
  --color-primary-300: #edb76f;
  --color-primary-400: #e69c3a;
  --color-primary-500: #d4af37;
  --color-primary-600: #b8941f;
  --color-primary-700: #9a7b1a;
  --color-primary-800: #7c6216;
  --color-primary-900: #5e4a11;

  --color-secondary-50: #f8f7ff;
  --color-secondary-100: #f0edff;
  --color-secondary-200: #e0d9ff;
  --color-secondary-300: #c9bbff;
  --color-secondary-400: #a78bff;
  --color-secondary-500: #8b5cf6;
  --color-secondary-600: #7c3aed;
  --color-secondary-700: #6d28d9;
  --color-secondary-800: #5b21b6;
  --color-secondary-900: #4c1d95;

  --color-accent-50: #f0f9ff;
  --color-accent-100: #e0f2fe;
  --color-accent-200: #bae6fd;
  --color-accent-300: #7dd3fc;
  --color-accent-400: #38bdf8;
  --color-accent-500: #0ea5e9;
  --color-accent-600: #0284c7;
  --color-accent-700: #0369a1;
  --color-accent-800: #075985;
  --color-accent-900: #0c4a6e;

  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;

  --color-luxury-gold: #d4af37;
  --color-luxury-silver: #c0c0c0;
  --color-luxury-platinum: #e5e4e2;
  --color-luxury-rose: #e8b4b8;
  --color-luxury-diamond: #b9f2ff;

  /* Typography */
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-serif: 'Playfair Display', serif;
  --font-display: 'Montserrat', sans-serif;

  /* Spacing */
  --spacing-18: 4.5rem;
  --spacing-88: 22rem;
  --spacing-128: 32rem;

  /* Border Radius */
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;

  /* Shadows */
  --shadow-luxury: 0 10px 25px -3px rgba(212, 175, 55, 0.1), 0 4px 6px -2px rgba(212, 175, 55, 0.05);
  --shadow-luxury-lg: 0 20px 25px -5px rgba(212, 175, 55, 0.1), 0 10px 10px -5px rgba(212, 175, 55, 0.04);
  --shadow-glow: 0 0 20px rgba(212, 175, 55, 0.3);
  --shadow-inner-glow: inset 0 2px 4px 0 rgba(212, 175, 55, 0.06);

  /* Animations */
  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.5s ease-out;
  --animate-slide-down: slideDown 0.5s ease-out;
  --animate-scale-in: scaleIn 0.3s ease-out;
  --animate-shimmer: shimmer 2s linear infinite;
  --animate-float: float 3s ease-in-out infinite;
  --animate-pulse-glow: pulseGlow 2s ease-in-out infinite;

  /* Background Images */
  --bg-luxury-gradient: linear-gradient(135deg, #d4af37 0%, #f4d4a7 50%, #d4af37 100%);
  --bg-hero-pattern: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d4af37' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Keyframes */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  0% { transform: translateY(-20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.9); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulseGlow {
  0%, 100% { box-shadow: 0 0 20px rgba(212, 175, 55, 0.3); }
  50% { box-shadow: 0 0 30px rgba(212, 175, 55, 0.5); }
}

/* Base Styles */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Utility Classes */
.luxury-gradient {
  background: var(--bg-luxury-gradient);
}

.hero-pattern {
  background-image: var(--bg-hero-pattern);
}

.shadow-luxury {
  box-shadow: var(--shadow-luxury);
}

.shadow-luxury-lg {
  box-shadow: var(--shadow-luxury-lg);
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

.shadow-inner-glow {
  box-shadow: var(--shadow-inner-glow);
}

.animate-fade-in {
  animation: var(--animate-fade-in);
}

.animate-slide-up {
  animation: var(--animate-slide-up);
}

.animate-slide-down {
  animation: var(--animate-slide-down);
}

.animate-scale-in {
  animation: var(--animate-scale-in);
}

.animate-shimmer {
  animation: var(--animate-shimmer);
}

.animate-float {
  animation: var(--animate-float);
}

.animate-pulse-glow {
  animation: var(--animate-pulse-glow);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-500);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-600);
}

/* Selection */
::selection {
  background: var(--color-primary-200);
  color: var(--color-primary-900);
}
